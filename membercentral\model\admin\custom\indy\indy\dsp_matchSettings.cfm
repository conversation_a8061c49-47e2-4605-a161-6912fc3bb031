<cfsavecontent variable="local.matchSettingsJS">
	<cfoutput>
	<script language="javascript">
		/* functions for field set selector widget */
		function getAvailableAndSelectedFieldSetsForParticipant(onCompleteFunc){
			let objParams = { siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','getAvailableAndSelectedFieldSetsJSON',objParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}
		function createMemberFieldUsageForParticipant(fsID, onCompleteFunc) {
			let objParams = { fieldSetID:fsID, siteResourceID:#local.siteResourceID#, area:'matchSettings' };
			TS_AJX('FIELDSETWIDGET','createMemberFieldUsage',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
		function removeParticipantFieldUsage(useID, onCompleteFunc) {
			var objParams = { useID:useID };
			TS_AJX('FIELDSETWIDGET','fsRemove',objParams,onCompleteFunc,onCompleteFunc,20000,onCompleteFunc);
		}
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.matchSettingsJS)#">

<cfoutput>
<h4>Match Settings</h4>
<h5 class="mt-3">Participant Classifications</h5>
#local.participantGroupSetWidget.html#

<h5 class="mt-4">Participant Field Set</h5>
<div class="mb-2">Use these Member Field Sets for the "Match Tool" filter:</div>
#local.strParticipantFieldsSelector.html#
</cfoutput>