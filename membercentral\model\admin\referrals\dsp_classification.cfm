<cfoutput>
<cfsavecontent variable="local.pageJS">
	<script language="javascript">
		function getReferralClassificationsData_referralClassificationsGroupSets(onCompleteFunc) {
			var ajaxParams = {
				siteResourceID: 0,
				ID: #local.referralID#
			};
			TS_AJX('GROUPSETWIDGET','getAvailableAndSelectedGroupSetsJSON',ajaxParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}

		function reloadReferralClassificationTable() {
			loadGroupSetGrids_referralClassificationsGroupSets();
		}

		function deleteReferralClassification(classificationID, onCompleteFunc) {
			var objParams = { classificationID: classificationID };
			TS_AJX('ADMINREFERRALS','deleteClassification',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}

		function moveReferralClassification(classificationID, direction, onCompleteFunc) {
			var objParams = { classificationID: classificationID, dir: direction };
			TS_AJX('ADMINREFERRALS','doClassificationMove',objParams,onCompleteFunc,onCompleteFunc,10000,onCompleteFunc);
		}
		function editClassification(cID){
			editClassification_referralClassificationsGroupSets(0, cID);
		}
	</script>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.pageJS)#">

<div class="card mb-1">
	<div class="card-header bg-light p-1">
		<div class="card-header--title font-size-lg ml-2">Classifications</div>
	</div>
	<div class="card-body p-1">
		#local.referralsGroupSetWidget.html#
	</div>
</div>
</cfoutput>