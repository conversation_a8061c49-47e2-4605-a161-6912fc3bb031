<cfsavecontent variable="local.gsSelectorJS">
	<cfoutput>
	<script language="javascript">
		function loadGroupSetGrids_#arguments.selectorID#() {
			let loadGSResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					let selectedGSListSource = $('##mc_SelectedGSList_#arguments.selectorID#').html();
					let selectedGSListTemplate = Handlebars.compile(selectedGSListSource);
					$('##selectedGSGridContainer_#arguments.selectorID#').html(selectedGSListTemplate(r));
					
					let availGSListSource = $('##mc_AvailGSList_#arguments.selectorID#').html();
					let availGSListTemplate = Handlebars.compile(availGSListSource);
					$('##availGSGridContainer_#arguments.selectorID#').html(availGSListTemplate(r));
					
					mcActivateTooltip($('##selectedGSGridContainer_#arguments.selectorID#'));
					mcActivateTooltip($('##availGSGridContainer_#arguments.selectorID#'));
					
					updateGroupSetCounts_#arguments.selectorID#(r);
				}
			};
			getGroupSetsJSON_#arguments.selectorID#(loadGSResult);
		}

		function updateGroupSetCounts_#arguments.selectorID#(data) {
			var selectedCount = data.arrselectedgroupsets ? data.arrselectedgroupsets.length : 0;
			var availableCount = data.arravailablegroupsets ? data.arravailablegroupsets.length : 0;
			
			$('##selectedGSGridContainer_#arguments.selectorID#').closest('.card').find('.selGSCount').text(selectedCount);
			$('##availGSGridContainer_#arguments.selectorID#').closest('.card').find('.availGSCount').text(availableCount);
			
			if (selectedCount > 0) {
				$('.gsCountSpan').removeClass('d-none');
				$('.gsCountLoadingSpan').addClass('d-none');
			}
		}

		function editGroupSet_#arguments.selectorID#(gsID) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: gsID > 0 ? 'Edit Group Set' : 'Create a Group Set',
				iframe: true,
				contenturl: '#local.editGroupSetLink#&gsID=' + gsID,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: 'saveMemGroupSet_#arguments.selectorID#',
					extrabuttonlabel: 'Save'
				}
			});
		}

		function saveMemGroupSet_#arguments.selectorID#() {
			$('##MCModalBodyIframe')[0].contentWindow.validateAndSaveGroupSet();
		}

		function previewGroupSet_#arguments.selectorID#(gsID) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'lg',
				title: 'Preview Group Set',
				contenturl: '#local.previewGroupSetLink#&gsID=' + gsID,
				strmodalfooter: { showclose: true }
			});
		}

		function editClassification_#arguments.selectorID#(gsID, classificationID) {
			var isReferrals = '#arguments.selectorID#'.toLowerCase().indexOf('referral') !== -1;
			var contentURL = '';

			if (isReferrals) {
				contentURL = '#local.editClassificationLink#&classificationID=' + classificationID + '&groupSetID=' + gsID;
			} else {
				var srID = #arguments.siteResourceID#;
				contentURL = '#local.editClassificationLink#&classificationID=' + classificationID + '&siteResourceID=' + srID + '&groupSetID=' + gsID;
			}
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: classificationID > 0 ? 'Edit Classification' : 'Add Classification',
				iframe: true,
				contenturl: contentURL,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmClassification :submit").click',
					extrabuttonlabel: 'Save'
				}
			});
		}
		
		<cfif arguments.area EQ 'details'>
			function reloadDetailsClassificationsTable(){
				loadGroupSetGrids_#arguments.selectorID#();
			}
		<cfelseif arguments.area EQ 'prospectcontact'>
			function reloadProspectContactClassificationsTable(){
				loadGroupSetGrids_#arguments.selectorID#();
			}
		<cfelseif arguments.selectorID EQ 'memberSettingsLinkedGroupSets'>
			function reloadLinkedClassificationsTable(){
				loadGroupSetGrids_#arguments.selectorID#();
			}
		<cfelseif arguments.selectorID EQ 'indyMatchSettingsGroupSets'>
			function reloadINDYClassifications(){
				loadGroupSetGrids_#arguments.selectorID#();
			}
		<cfelse>
			function reloadClassificationsTable(){
				loadGroupSetGrids_#arguments.selectorID#();
			}
		</cfif>

		function moveGroupSetUp_#arguments.selectorID#(classificationID) {
			let moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				}
			};
			TS_AJX('GROUPSETWIDGET','moveGroupSet',{classificationID: classificationID, direction: 'up', selectorID: '#arguments.selectorID#'},moveResult,moveResult,60000,moveResult);
		}

		function moveGroupSetDown_#arguments.selectorID#(classificationID) {
			let moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				}
			};
			TS_AJX('GROUPSETWIDGET','moveGroupSet',{classificationID: classificationID, direction: 'down', selectorID: '#arguments.selectorID#'},moveResult,moveResult,60000,moveResult);
		}

		function addGroupSetToSelected_#arguments.selectorID#(gsID) {
			editClassification_#arguments.selectorID#(gsID, 0);
		}

		function removeGroupSetFromSelected_#arguments.selectorID#(classificationID,gsid) {
			let removeResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					loadGroupSetGrids_#arguments.selectorID#();
				}
			};
			let delBtn = $('##removeGSBtn_#arguments.selectorID#_'+gsid);
			mca_initConfirmButton(delBtn, function(){
				// Detect context and use appropriate AJAX component
				var selectorID = '#arguments.selectorID#'.toLowerCase();
				var isReferrals = selectorID.indexOf('referral') !== -1;
				var isMemberDirectory = selectorID.indexOf('memberdirectory') !== -1;
				var ajaxComponent, objParams;

				if (isReferrals) {
					ajaxComponent = 'ADMINREFERRALS';
					objParams = { classificationID: classificationID };
				} else if (isMemberDirectory) {
					ajaxComponent = 'ADMMEMBERDIRECTORY';
					objParams = {
						classificationID: classificationID,
						memberDirectoryID: #arguments.ID#,
						memberDirectorySRID: 0 // Will be populated by the server
					};
				} else {
					ajaxComponent = 'ADMMEMBERSETTINGS';
					objParams = { classificationID: classificationID };
				}

				TS_AJX(ajaxComponent,'deleteClassification',objParams,removeResult,removeResult,10000,removeResult);
			});
		}

		function getGroupSetsJSON_#arguments.selectorID#(onCompleteFunc){
			var ajaxParams = {
				selectorID: "#arguments.selectorID#"
				<cfif arguments.siteResourceID>, siteResourceID: "#arguments.siteResourceID#"</cfif>
				<cfif arguments.ID>, ID: "#arguments.ID#"</cfif>
				<cfif len(trim(arguments.area))>, area: "#arguments.area#"</cfif>
			};
			TS_AJX('GROUPSETWIDGET','getAvailableAndSelectedGroupSetsJSON',ajaxParams,onCompleteFunc,onCompleteFunc,60000,onCompleteFunc);
		}

		$(function() {
			loadGroupSetGrids_#arguments.selectorID#();
		});
	</script>
	<style>
		##availGSAccordion_#arguments.selectorID# > .card { box-shadow: none; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnPreviewGS { padding-right: 0.3rem; padding-left: 0.3rem; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnEditGS { padding-right: 0.32rem; padding-left: 0.32rem; }
		##gsGridContainer_#arguments.selectorID# .btn-xs.btnAddGS { padding-right: 0.4rem; padding-left: 0.4rem; }
		##gsGridContainer_#arguments.selectorID# .table td { vertical-align: middle; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.gsSelectorJS#">

<cfoutput>
<div class="row no-gutters" id="gsGridContainer_#arguments.selectorID#">
	<div class="col-12">
		<div class="card card-box">
			<div class="card-header bg-light">
				<div class="card-header--title font-weight-bold font-size-sm">
					Selected Group Sets
				</div>
				<span class="gsCountSpan small d-none"><span class="selGSCount pr-1"></span>selected</span>
				<span class="gsCountLoadingSpan small">loading..</span>
			</div>
			<div class="card-body p-0">
				<div id="selectedGSGridContainer_#arguments.selectorID#" style="height:#arguments.selectedGSGridHeight#px;overflow-y:auto;">
				</div>
				<div class="accordion" id="availGSAccordion_#arguments.selectorID#">
					<div class="card card-box rounded-bottom">
						<div class="card-header bg-light rounded-0" id="availHeading_#arguments.selectorID#">
							<button class="btn btn-link d-flex align-items-center justify-content-between collapsed" type="button" data-toggle="collapse" data-target="##availCollapse_#arguments.selectorID#" aria-expanded="false" aria-controls="availCollapse_#arguments.selectorID#">
								<span class="font-size-sm"><span class="availGSCount pr-1"></span>Additional Group Sets Available</span>
								<div class="d-flex align-items-center">
									<i class="fa-solid fa-caret-up font-size-xl"></i>
								</div>
							</button>
						</div>
						<div id="availCollapse_#arguments.selectorID#" class="collapse" aria-labelledby="availHeading_#arguments.selectorID#" data-parent="##availGSAccordion_#arguments.selectorID#">
							<div class="card-body p-0">
								<div class="card-header bg-light d-flex align-items-center justify-content-between font-size-xs">
									<span class="font-italic">Choose From Available Group Sets</span>
									<a href="##" name="btnCreateGroupSet" onclick="editGroupSet_#arguments.selectorID#(0);" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="Create a Group Set">
										<i class="fa-regular fa-circle-plus fa-lg"></i>
									</a>
								</div>
								<div id="availGSGridContainer_#arguments.selectorID#" style="height:300px;overflow-y:auto;" class="bg-secondary card-body p-2">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	</div>
</div>

<!-- Handlebars Template for Selected Group Sets -->
<script id="mc_SelectedGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	{{##if arrselectedgroupsets}}
		<table class="table table-sm table-borderless table-striped mb-0">
		{{##each arrselectedgroupsets}}
			<tr class="border-bottom">
				<cfset local.actionColWidthPct = 33>
				<cfset local.nameColWidthPct = 100 - local.actionColWidthPct>
				<td width="#local.nameColWidthPct#%" class="pl-2 font-size-sm">
					<span>{{groupsetname}}</span>
				</td>
				<td width="#local.actionColWidthPct#%" class="text-right">
					<span class="d-inline-block">
						<a href="##" id="moveUpGSBtn_#arguments.selectorID#_{{classificationid}}"
							{{##compare @index '!=' 0}}
								class="btn btn-xs btn-outline-dark"
								onclick="$(this).tooltip('hide');moveGroupSetUp_#arguments.selectorID#({{classificationid}});return false;"
								data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Up"
							{{/compare}}
							{{##compare @index '==' 0}}
								class="btn btn-xs btn-outline-dark invisible"
							{{/compare}}
							>
							<i class="fa-solid fa-up"></i>
						</a>
						<a href="##" id="moveDownGSBtn_#arguments.selectorID#_{{classificationid}}"
							{{##compare (math @index "+" 1) '!=' ../arrselectedgroupsets.length}}
								class="btn btn-xs btn-outline-dark"
								onclick="$(this).tooltip('hide');moveGroupSetDown_#arguments.selectorID#({{classificationid}});return false;"
								data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Move Group Set Down"
							{{/compare}}
							{{##compare (math @index "+" 1) '==' ../arrselectedgroupsets.length}}
								class="btn btn-xs btn-outline-dark invisible"
							{{/compare}}
							>
							<i class="fa-solid fa-down"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Group Set">
							<i class="fa-solid fa-pencil"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Group Set">
							<i class="fa-solid fa-eye"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" onclick="$(this).tooltip('hide');editClassification_#arguments.selectorID#({{groupsetid}}, {{classificationid}});return false;" class="btn btn-xs btn-outline-warning" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Classification Settings">
							<i class="fa-solid fa-gear"></i>
						</a>
					</span>
					<span class="d-inline-block">
						<a href="##" id="removeGSBtn_#arguments.selectorID#_{{groupsetid}}" onclick="$(this).tooltip('hide');removeGroupSetFromSelected_#arguments.selectorID#({{classificationid}}, {{groupsetid}});return false;" class="btn btn-xs btn-outline-danger" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Remove this Group Set From Selections">
							<i class="fa-solid fa-trash-can"></i>
						</a>
					</span>
				</td>
			</tr>
		{{/each}}
		</table>
	{{else}}
		<div class="text-center py-3">No Group Sets Selected.</div>
	{{/if}}
</script>

<!-- Handlebars Template for Available Group Sets -->
<script id="mc_AvailGSList_#arguments.selectorID#" type="text/x-handlebars-template">
	<ul class="list-group list-group-flush list-group-#arguments.selectorID#">
		{{##each arravailablegroupsets}}
		<li class="list-group-item d-flex align-items-center justify-content-between p-2">
			<div class="flex-grow-1">
				<span>{{groupsetname}}</span>
			</div>
			<div class="col-auto pl-2" role="group">
				<a href="##" id="btnAddGS_#arguments.selectorID#_{{groupsetid}}" onclick="$(this).tooltip('hide');addGroupSetToSelected_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-success btnAddGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Add this Group Set">
					<i class="fa-solid fa-plus"></i>
				</a>
				<a href="##" onclick="$(this).tooltip('hide');editGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-primary btnEditGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Edit this Group Set">
					<i class="fa-solid fa-pencil"></i>
				</a>
				<a href="##" onclick="$(this).tooltip('hide');previewGroupSet_#arguments.selectorID#({{groupsetid}});return false;" class="btn btn-xs btn-outline-info btnPreviewGS" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" title="Preview this Group Set">
					<i class="fa-solid fa-eye"></i>
				</a>
			</div>
		</li>
		{{/each}}
		{{##unless arravailablegroupsets.length}}
		<li class="list-group-item text-center text-muted p-3">
			<i class="fa-regular fa-folder-open fa-2x d-block mb-2"></i>
			No group sets available
		</li>
		{{/unless}}
	</ul>
</script>
</cfoutput>
