<cfcomponent extends="model.admin.admin" output="no">
	<cfscript>
		defaultEvent = 'controller';
	</cfscript>
	
	<cffunction name="controller" access="public" output="false" returntype="struct" hint="controller for this app">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			// set rights into event -------------------------------------------------------------------- ::
			local.tmpRights = buildRightAssignments(siteResourceID=this.siteResourceID, memberID=session.cfcuser.memberdata.memberID, siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			arguments.event.getCollection()['mc_admintoolInfo']['myRights'] = local.tmpRights;
			// build quick links ------------------------------------------------------------------------ ::
			this.link.edit = buildCurrentLink(arguments.event,"edit");
			this.link.message = buildCurrentLink(arguments.event,"message");
			this.link.saveSettings = buildCurrentLink(arguments.event,"saveSettings");
			
			this.link.editClassification = buildCurrentLink(arguments.event,"editClassification") & "&mode=direct";
			this.link.saveClassification = buildCurrentLink(arguments.event,"saveClassification") & "&mode=stream";
			this.link.searchEmailActivity = buildCurrentLink(arguments.event,"searchEmailActivity");
			this.link.showEmailActivity = buildCurrentLink(arguments.event,"showEmailActivity");
			this.link.showEstimatedMonthlyBilling = buildCurrentLink(arguments.event,"showEstimatedMonthlyBilling") & "&mode=direct";

			this.memberAdminSiteResourceID = arguments.event.getValue('mc_siteinfo.memberAdminSiteResourceID');
		</cfscript>
		
		<cfscript>
			// Run Assigned Method ---------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('mca_ta')];
			// pass the argument collection to the current method and execute it. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="edit" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();
			local.siteID = arguments.event.getValue('mc_siteInfo.siteID');
			local.orgID = arguments.event.getValue('mc_siteInfo.orgID');
			local.objFieldSetSelector = createObject("component","model.admin.common.modules.fieldSetSelector.fieldSetSelector");
			local.objMemberFieldsets = createObject("component","model.admin.memberFieldSets.memberFieldSets");
			local.objGroupSetSelector = CreateObject("component","model.admin.common.modules.groupSetSelector.groupSetSelector");

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSettingsUpdate = checkRights(arguments.event,'ViewMemberSettingsAdmin');
			if (NOT local.security.editSettingsUpdate)
				application.objCommon.redirect('#this.link.message#&message=1');
			
			local.permsGotoLink = buildLinkToTool(toolType='PermissionsAdmin',mca_ta='showPerms') & '&mode=direct';
			local.manageGroupsLink = buildLinkToTool(toolType='GroupAdmin',mca_ta='list');
			local.editClassificationLink = buildCurrentLink(arguments.event,"editClassification") & "&mode=direct";
			// Build breadCrumb Trail ------------------------------------------------------------------- ::
			appendBreadCrumbs(arguments.event,{ link='', text="Edit Settings" });
		</cfscript>
		
		<cfset local.adminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MemberSettingsAdmin',siteID=local.siteID)>
		
		<cfset local.qrySearchFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=this.memberAdminSiteResourceID, area='search', module="MemberSettings")>
		<cfif len(local.qrySearchFieldSet.fieldsetID) eq 0>
			<cfset local.searchFieldsetID = 0>
		<cfelse>
			<cfset local.searchFieldsetID = local.qrySearchFieldSet.fieldsetID>
		</cfif>
		<cfif len(local.qrySearchFieldSet.useID) eq 0>
			<cfset local.searchUseID = 0>
		<cfelse>
			<cfset local.searchUseID = local.qrySearchFieldSet.useID>
		</cfif>
		
		<cfset local.qryResultsFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=this.memberAdminSiteResourceID, area='results', module="MemberSettings")>
		<cfif len(local.qryResultsFieldSet.fieldsetID) eq 0>
			<cfset local.resultsFieldsetID = 0>
		<cfelse>
			<cfset local.resultsFieldsetID = local.qryResultsFieldSet.fieldsetID>
		</cfif>
		<cfif len(local.qryResultsFieldSet.useID) eq 0>
			<cfset local.resultsUseID = 0>
		<cfelse>
			<cfset local.resultsUseID = local.qryResultsFieldSet.useID>
		</cfif>

		<cfset local.qryNewAcctFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=this.memberAdminSiteResourceID, area='newacct', module="MemberSettings")>
		<cfif len(local.qryNewAcctFieldSet.fieldsetID) eq 0>
			<cfset local.newacctFieldsetID = 0>
		<cfelse>
			<cfset local.newacctFieldsetID = local.qryNewAcctFieldSet.fieldsetID>
		</cfif>
		<cfif len(local.qryNewAcctFieldSet.useID) eq 0>
			<cfset local.newacctUseID = 0>
		<cfelse>
			<cfset local.newacctUseID = local.qryNewAcctFieldSet.useID>
		</cfif>
		
		<cfset local.qryMainFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=this.memberAdminSiteResourceID, area='main', module="MemberSettings")>
		<cfif len(local.qryMainFieldSet.fieldsetID) eq 0>
			<cfset local.mainFieldsetID = 0>
		<cfelse>
			<cfset local.mainFieldsetID = local.qryMainFieldSet.fieldsetID>
		</cfif>
		<cfif len(local.qryMainFieldSet.useID) eq 0>
			<cfset local.mainUseID = 0>
		<cfelse>
			<cfset local.mainUseID = local.qryMainFieldSet.useID>
		</cfif>
		
		<cfset local.qryLinkedRecordFieldSet = local.objMemberFieldsets.getSettingsFieldsetID(siteResourceID=local.adminSiteResourceID, area='linkedrecord', module="MemberSettings")>
		<cfif len(local.qryLinkedRecordFieldSet.fieldsetID) eq 0>
			<cfset local.linkedRecordFieldsetID = 0>
		<cfelse>
			<cfset local.linkedRecordFieldsetID = local.qryLinkedRecordFieldSet.fieldsetID>
		</cfif>
		<cfif len(local.qryLinkedRecordFieldSet.useID) eq 0>
			<cfset local.linkedRecordUseID = 0>
		<cfelse>
			<cfset local.linkedRecordUseID = local.qryLinkedRecordFieldSet.useID>
		</cfif>
		
		<cfset local.appInstanceSettings = super.getInstanceSettings(this.appInstanceID)>
		<cfset local.showMemberPhotosInSearchResults = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="showMemberPhotosInSearchResults"]/@value)')>
		<cfset local.numPerPageInSearchResults = xmlSearch(local.appInstanceSettings.settingsXML,'string(/settings/setting[@name="numPerPageInSearchResults"]/@value)')>

		<cfset local.classificationsDTRootLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberSettingsJSON&meth=getClassifications&mode=stream">
		<cfset local.classificationsLink = "#local.classificationsDTRootLink#&siteResourceID=#this.memberAdminSiteResourceID#">
		<cfset local.linkedRecordClassificationsLink = "#local.classificationsDTRootLink#&siteResourceID=#local.adminSiteResourceID#&linked=1">
		
		<cfset local.strSearchFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="SearchFieldSet", selectedValue=local.searchFieldsetID)>
		<cfset local.strResultsFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="ResultsFieldSet", selectedValue=local.resultsFieldsetID)>
		<cfset local.strMainTabFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="MainFieldSet", selectedValue=local.mainFieldsetID)>
		<cfset local.strNewAcctFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="NewAcctFieldSet", selectedValue=local.newacctFieldsetID)>
		<cfset local.strLinkedRecordFSSelector = local.objFieldSetSelector.getFieldSetSelector(siteID=local.siteID, selectorID="linkedRecordFieldSet", selectedValue=local.linkedRecordFieldsetID)>

		<cfset local.strCustomFieldsSelector = local.objFieldSetSelector.getMultipleFieldSetSelector(
			selectorID="fsSelectorForCustomTab",
			getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForCustomTab",
			addFieldSetUsageFunc="createMemberFieldUsageForCustomTab",
			removeFieldSetUsageFunc="removeMemberFieldUsage",
			hasPermissionAction=true,
			hasOrderingAction=true,
			orderFieldSetFunc="moveCustomFSRow")>

		<cfset local.strCopyInfoFieldsSelector = local.objFieldSetSelector.getMultipleFieldSetSelector(
			selectorID="fsSelectorForCopyInfo",
			getFieldSetDataFunc="getAvailableAndSelectedFieldSetsForCopyInfo",
			addFieldSetUsageFunc="createMemberFieldUsageForCopyInfo",
			removeFieldSetUsageFunc="removeMemberFieldUsage")>
			
		<cfset local.groupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
			siteID = local.siteID,
			orgID = local.orgID,
			selectorID = "memberSettingsGroupSets",
			siteResourceID = this.memberAdminSiteResourceID,
			selectedGSGridHeight = 165,
			area="details",
			editClassificationToolType = "MemberSettingsAdmin"
		)>

		<cfset local.linkedGroupSetWidget = local.objGroupSetSelector.getMultipleGroupSetSelector(
			siteID = local.siteID,
			orgID = local.orgID,
			selectorID = "memberSettingsLinkedGroupSets",
			siteResourceID = local.adminSiteResourceID,
			selectedGSGridHeight = 165,
			area="details",
			editClassificationToolType = "MemberSettingsAdmin"
		)>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_edit.cfm">
			</cfoutput>
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="saveSettings" access="public" output="false" returntype="void">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			local.data = "";

			// PAGE SECURITY ---------------------------------------------------------------------------- ::
			local.security.editSettingsUpdate = checkRights(arguments.event,'ViewMemberSettingsAdmin');
			if (NOT local.security.editSettingsUpdate) 
				application.objCommon.redirect('#this.link.message#&message=1');

			// validation
			if (NOT val(arguments.event.getValue('SearchFieldSet')))
				application.objCommon.redirect('#this.link.message#&message=2');
			if (NOT val(arguments.event.getValue('ResultsFieldSet')))
				application.objCommon.redirect('#this.link.message#&message=3');
		</cfscript>
		
		<cfset local.adminSiteResourceID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='MemberSettingsAdmin',siteID=arguments.event.getValue('mc_siteinfo.siteid'))>

		<cfquery datasource="#application.dsn.membercentral.dsn#">
			SET XACT_ABORT, NOCOUNT ON;
			BEGIN TRY
				BEGIN TRAN;
					UPDATE dbo.ams_memberFieldUsage
					SET fieldsetID = <cfqueryparam value="#val(arguments.event.getValue('SearchFieldSet'))#" cfsqltype="CF_SQL_INTEGER">
					WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('searchUseID'))#" cfsqltype="CF_SQL_INTEGER">;
			
					UPDATE dbo.ams_memberFieldUsage
					SET fieldsetID = <cfqueryparam value="#val(arguments.event.getValue('ResultsFieldSet'))#" cfsqltype="CF_SQL_INTEGER">
					WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('resultsUseID'))#" cfsqltype="CF_SQL_INTEGER">;
			
					update ai
					set settingsXML.modify('replace value of (/settings/setting[@name=''showMemberPhotosInSearchResults'']/@value)[1] with ''#arguments.event.getValue('showMemberPhotosInSearchResults','true')#''')
					from dbo.cms_applicationInstances as ai
					where ai.applicationInstanceName = 'admin'
					and ai.siteid = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;
			
					update ai
					set settingsXML.modify('replace value of (/settings/setting[@name=''numPerPageInSearchResults'']/@value)[1] with ''#arguments.event.getValue('numPerPageInSearchResults',25)#''')
					from dbo.cms_applicationInstances as ai
					where ai.applicationInstanceName = 'admin'
					and ai.siteid = <cfqueryparam value="#arguments.event.getValue('mc_siteinfo.siteid')#" cfsqltype="CF_SQL_INTEGER">;

					<cfif arguments.event.getValue('origMainFieldSet',0) neq arguments.event.getValue('MainFieldSet',0)>
						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('mainUseID'))#" cfsqltype="CF_SQL_INTEGER">;
					
						<cfif arguments.event.getValue('MainFieldSet',0) gt 0>
							INSERT INTO ams_memberFieldUsage (siteResourceID,fieldsetID,area,fieldSetOrder)
							VALUES (
								<cfqueryparam value="#this.memberAdminSiteResourceID#" cfsqltype="CF_SQL_INTEGER">,
								<cfqueryparam value="#val(arguments.event.getValue('MainFieldSet'))#" cfsqltype="CF_SQL_INTEGER">,
								'main',
								1);
						</cfif>
					</cfif>
			
					<cfif arguments.event.getValue('origNewAcctFieldSet',0) neq arguments.event.getValue('NewAcctFieldSet',0)>
						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('newAcctUseID'))#" cfsqltype="CF_SQL_INTEGER">;
					
						<cfif arguments.event.getValue('NewAcctFieldSet',0) gt 0>
							INSERT INTO ams_memberFieldUsage (siteResourceID,fieldsetID,area,fieldSetOrder)
							VALUES (
								<cfqueryparam value="#this.memberAdminSiteResourceID#" cfsqltype="CF_SQL_INTEGER">,
								<cfqueryparam value="#val(arguments.event.getValue('NewAcctFieldSet'))#" cfsqltype="CF_SQL_INTEGER">,
								'newacct',
								1);
						</cfif>
					</cfif>
				
					<cfif arguments.event.getValue('origLinkedRecordFieldSet',0) neq arguments.event.getValue('linkedRecordFieldSet',0)>
						DELETE FROM	dbo.ams_memberFieldUsage
						WHERE useID = <cfqueryparam value="#val(arguments.event.getValue('linkedRecordUseID'))#" cfsqltype="CF_SQL_INTEGER">;
						
						<cfif arguments.event.getValue('linkedRecordFieldSet',0) gt 0>
							INSERT INTO ams_memberFieldUsage (siteResourceID,fieldsetID,area,fieldSetOrder)
							VALUES (
								<cfqueryparam value="#local.adminSiteResourceID#" cfsqltype="CF_SQL_INTEGER">,
								<cfqueryparam value="#val(arguments.event.getValue('linkedRecordFieldSet'))#" cfsqltype="CF_SQL_INTEGER">,
								'linkedrecord',
								1);
						</cfif>
					</cfif>
				COMMIT TRAN;
			END TRY
			BEGIN CATCH
				IF @@trancount > 0 ROLLBACK TRANSACTION;
				EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
			END CATCH
		</cfquery>

		<cflocation url="#this.link.edit#" addtoken="no">
	</cffunction>
	
	<cffunction name="editClassification" access="public" output="false" returntype="struct" hint="Add Sub-Panel page">
		<cfargument name="Event" type="any" />
		<cfscript>
			var local = structNew();
			local.rc = arguments.event.getCollection();	

			local.classificationID = arguments.event.getValue('classificationID',0);
			local.gsid = arguments.event.getValue('groupSetID',0);
			local.selectorID = arguments.event.getValue('selectorID','');
			local.qryGetClassification = getClassificationByID(local.classificationID);
			local.nameOverride = local.qryGetClassification.name;
			local.siteResourceID = arguments.event.getValue('siteResourceID',0);
			local.allowSearch = local.qryGetClassification.allowSearch;
			local.showInSearchResults = local.qryGetClassification.showInSearchResults;
			local.groupSetID = local.qryGetClassification.groupSetID;
			local.pageTitle = "Add Classification";
			local.qryGroupSets = CreateObject("component","model.admin.MemberGroupSets.MemberGroupSets").getGroupSets(orgID=arguments.event.getValue('mc_siteInfo.orgID'));
			if(local.classificationID)
				local.pageTitle = "Edit Classification";
		</cfscript>
	
		<cfsavecontent variable="local.data">
			<cfoutput>
				<cfinclude template="frm_classification.cfm" />
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>

	<cffunction name="addFeaturedGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.addFeaturedGroup">
				SET NOCOUNT ON;

				DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">;
				DECLARE @groupID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">;

				IF NOT EXISTS (select featuredGroupID from dbo.ams_featuredGroups WHERE orgID = @orgID and groupID = @groupID)
					INSERT INTO dbo.ams_featuredGroups (orgID, groupID)
					VALUES (@orgID, @groupID);
			</cfquery>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="removeFeaturedGroup" access="public" output="false" returntype="struct">
		<cfargument name="mcproxy_orgID" type="numeric" required="yes">
		<cfargument name="groupID" type="numeric" required="yes">

		<cfset var local = structNew()>

		<cftry>
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.removeFeaturedGroup">
				delete from dbo.ams_featuredGroups
				where orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.mcproxy_orgID#">
				AND groupID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.groupID#">
			</cfquery>
			
			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>
		
		<cfreturn local.data>
	</cffunction>

	<cffunction name="getClassifications" access="public" output="false" returntype="query">
		<cfargument name="siteResourceID" type="numeric"  required="true">
		
		<cfset var local = structNew()>
		
		<cfquery name="local.qryGetClassifications" datasource="#application.dsn.membercentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			select c.classificationid, c.name, mgs.groupSetName, c.allowSearch, c.siteResourceID, c.area,
				c.showInSearchResults, c.groupSetID, c.classificationOrder
			from dbo.ams_classifications c
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = c.groupSetID
			where c.siteResourceID = <cfqueryparam value="#arguments.siteResourceID#" cfsqltype="cf_sql_integer">
			order by c.classificationOrder;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>
		
		<cfreturn local.qryGetClassifications>
	</cffunction>
	
	<cffunction name="getClassificationByID" access="public" output="false" returntype="query">
		<cfargument name="classificationid" type="numeric"  required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.qryGetClassification" datasource="#application.dsn.membercentral.dsn#">
			select c.classificationid, c.name, mgs.groupSetName, c.allowSearch, c.siteResourceID, c.showInSearchResults, c.groupSetID
			from ams_classifications c
			inner join dbo.ams_memberGroupSets mgs on mgs.groupSetID = c.groupSetID
			where c.classificationid = <cfqueryparam value="#arguments.classificationid#" cfsqltype="cf_sql_integer" />
		</cfquery>

		<cfreturn local.qryGetClassification />
	</cffunction>
	
	<cffunction name="saveClassification" access="public"  returntype="struct">
		<cfargument name="Event" type="any" />

		<cfscript>
			var local = structNew();
			local.classificationID = arguments.event.getValue('classificationID');
		</cfscript>
		
		<cfif local.classificationID>
			<cfquery name="local.updateClassification" datasource="#application.dsn.membercentral.dsn#">
				update dbo.ams_classifications
				set
					<cfif arguments.event.valueExists('nameOverride') and len(trim(arguments.event.getValue('nameOverride')))>
						name = <cfqueryparam value="#arguments.event.getValue('nameOverride')#" cfsqltype="cf_sql_varchar" />,
					<cfelse>
						name = NULL,
					</cfif>
					<cfif arguments.event.valueExists('allowSearch') and val(arguments.event.getValue('allowSearch'))>
						allowSearch = 1,
					<cfelse>
						allowSearch = 0,
					</cfif>
					<cfif arguments.event.valueExists('showInSearchResults') and val(arguments.event.getValue('showInSearchResults'))>
						showInSearchResults = 1,
					<cfelse>
						showInSearchResults = 0,
					</cfif>
					<cfif arguments.event.valueExists('groupSetID') and val(arguments.event.getValue('groupSetID'))>
						groupSetID = <cfqueryparam value="#arguments.event.getValue('groupSetID')#" cfsqltype="cf_sql_integer"  />
					<cfelse>
						groupSetID = NULL
					</cfif>
				where classificationID = <cfqueryparam value="#arguments.event.getValue('classificationID')#" cfsqltype="cf_sql_integer" />
			</cfquery>
		<cfelse>
			<cfquery name="local.updateClassification" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;
				declare @newOrderItem int;
				
				select @newOrderItem = case when max(classificationOrder) is null then 1 else max(classificationOrder) + 1 end
				from dbo.ams_classifications
				where siteResourceID = <cfqueryparam value="#arguments.event.getValue('siteResourceID')#" cfsqltype="cf_sql_integer" />;
				
				insert into dbo.ams_classifications(
					name,
					allowSearch,
					siteResourceID,
					area,
					showInSearchResults,
					groupSetID,
					classificationOrder 
				)
				values (
					<cfif arguments.event.valueExists('nameOverride') and len(trim(arguments.event.getValue('nameOverride')))>
						<cfqueryparam value="#arguments.event.getValue('nameOverride')#" cfsqltype="cf_sql_varchar" />,
					<cfelse>
						NULL,
					</cfif>
					<cfqueryparam value="#arguments.event.getValue('allowSearch',0)#" cfsqltype="cf_sql_bit" />,
					<cfqueryparam value="#arguments.event.getValue('siteResourceID')#" cfsqltype="cf_sql_integer" />,
					'details',
					<cfqueryparam value="#arguments.event.getValue('showInSearchResults',0)#" cfsqltype="cf_sql_bit" />,
					<cfqueryparam value="#arguments.event.getValue('groupSetID')#" cfsqltype="cf_sql_integer" />,
					@newOrderItem
				);

				set nocount off;
			</cfquery>
		</cfif>
		
		<cfsavecontent variable="local.data">
			<cfoutput>
			<script language="javascript">debugger;
				top.reloadClassifications();
				top.MCModalUtils.hideModal();
			</script>
			</cfoutput>
		</cfsavecontent>
	
		<cfreturn returnAppStruct(local.data,"echo") />
	</cffunction>
	
	<cffunction name="deleteClassification" access="public" output="false" returntype="struct">
		<cfargument name="classificationID" type="numeric" />

		<cfset var local = structNew() />

		<cftry>
			<cfquery name="local.qryDelete" datasource="#application.dsn.membercentral.dsn#">
				set nocount on;

				declare	@classificationID int, @siteResourceID int;

				set @classificationID = <cfqueryparam value="#arguments.classificationID#" cfsqltype="cf_sql_integer" />;

				select @siteResourceID = siteResourceID
				from dbo.ams_classifications
				where classificationID = @classificationID;

				delete from dbo.ams_classifications
				where classificationID = @classificationID;

				exec dbo.ams_reorderClassifications @siteResourceID = @siteResourceID;

				set nocount off;
			</cfquery>

			<cfset local.data.success = true />
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="getGroupSetGroup" access="public" output="false" returntype="query">
		<cfargument name="groupSetID" type="numeric" required="true" />
		
		<cfset var local = structNew() />
		
		<cfquery name="local.getGroupSetGroup" datasource="#application.dsn.memberCentral.dsn#">
			select mgsg.groupSetGroupID, mgsg.groupSetID, mgsg.groupID, coalesce(mgsg.labelOverride, g.groupName) as groupLabel, g.groupPathExpanded as groupPath
			from dbo.ams_memberGroupSetGroups as mgsg
			inner join dbo.ams_groups as g on g.groupID = mgsg.groupID
			where mgsg.groupSetID = <cfqueryparam value="#arguments.groupSetID#" cfsqltype="cf_sql_integer" />
			order by groupLabel
		</cfquery>
		
		<cfreturn local.getGroupSetGroup />
	</cffunction>

	<cffunction name="createCustomMemberFieldUsage" access="public" output="false" returntype="struct">
		<cfargument name="fieldsetID" type="numeric" required="true">
		<cfargument name="siteResourceID" type="numeric" required="true">

		<cfset var local = structNew()>

		<cftry>
			<cfset local.qualifyRFID = application.objSiteResource.getResourceFunctionIDbyResourceName(resourceTypeName="memberFieldset", functionName="fsQualify")>

			<cfstoredproc datasource="#application.dsn.memberCentral.dsn#" procedure="ams_createCustomMemberFieldUsage">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.siteResourceID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.fieldSetID#">
				<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#local.qualifyRFID#">
				<cfprocparam type="out" cfsqltype="CF_SQL_INTEGER" variable="local.useID">
			</cfstoredproc>

			<cfset local.data.success = true>
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data>
	</cffunction>

	<cffunction name="searchEmailActivity" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfscript>
			var local = structNew();

			if (NOT arguments.event.getValue('mc_adminToolInfo.myRights.viewEmailActivity'))
				application.objCommon.redirect("#this.link.message#&message=1");

			local.fromDt = dateFormat(dateAdd("d",-7,now()), "m/d/yyyy");
			local.toDt = dateFormat(now(), "m/d/yyyy");

			local.activityExportLink = buildCurrentLink(arguments.event,"exportActivity") & "&mode=stream";
			local.memSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='list') & '&mode=direct';
			local.grpSelectLink = buildLinkToTool(toolType='MemberSelectorAdmin',mca_ta='listGroups') & '&mode=direct';
			local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit');
			local.resendEmailLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=resendEmail&mode=direct";
			local.downloadMessageAttachmentLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=downloadMessageAttachment&mode=stream";

			local.gridRootURL = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&mode=stream&meth=";
			local.messagesLink = '#local.gridRootURL#getMessages';
			local.recipientsLink = '#local.gridRootURL#getEmailRecipients&gridMode=recipientsGrid';
			local.futureMessagesToSend = checkFutureSendDateExists(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			
			local.qryOrgEmailTypes = application.objOrgInfo.getOrgEmailTypes(orgID=arguments.event.getValue('mc_siteinfo.orgID'));
			local.qryApplications = CreateObject('component', 'model.admin.emailBlast.emailBlast').getEmailMessageTypes(siteID=arguments.event.getValue('mc_siteinfo.siteid'));
			local.EmailBlastAdminSRID = application.objSiteResource.getSiteResourceIDForResourceType(resourceTypeName='EmailBlast', siteID=arguments.event.getValue('mc_siteInfo.siteid'));
			local.qryCategories = CreateObject('component', 'model.system.platform.category').getCategories(local.EmailBlastAdminSRID);
		</cfscript>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_emailActivity.cfm">
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportActivity" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<cfset local.fSentFrom = replace(arguments.event.getTrimValue('fSentFrom',''),' - ',' ')>
		<cfset local.fSentTo = replace(arguments.event.getTrimValue('fSentTo',''),' - ',' ')>
		<cfif len(local.fSentTo) gt 0 and len(local.fSentTo) lte 10>
			<cfset local.fSentTo = DateFormat(local.fSentTo,"m/d/yyyy") & " 23:59:59">
		</cfif>

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "EmailActivity.csv">

		<cfset local.qryActivity = CreateObject('component','model.admin.emailBlast.emailBlast').getMessagesFromFilters(messageTypeID=arguments.event.getTrimValue('messageType',0), 
			categoryID=arguments.event.getValue('fCategory',0), subCategoryIDList=arguments.event.getValue('fSubCategory',0), siteID=arguments.event.getValue('mc_siteinfo.siteid'), 
			fromName=arguments.event.getTrimValue('fromname',''), replyToEmail=arguments.event.getValue('replytoemail',''), subject=arguments.event.getTrimValue('subject',''), 
			fSentFrom=local.fSentFrom, fSentTo=local.fSentTo, emailTypeIDList=arguments.event.getTrimValue('emailType',''), 
			msgCountFrom=arguments.event.getTrimValue('msgCountFrom',''), msgCountTo=arguments.event.getTrimValue('msgCountTo',''), mode="export", 
			folderPathUNC=local.strFolder.folderPathUNC, reportFileName=local.reportFileName, fBlastTestMsg=arguments.event.getValue('fBlastTestMsg',''))>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportMessages('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="checkFutureSendDateExists" access="private" output="false" returntype="number">
		<cfargument name="siteID" type="numeric" required="true">
		
		<cfset var local = structNew()>

		<cfquery name="local.qryMessage" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.siteID#">;
			declare @today datetime = GETDATE();

			select count(m.messageid) as totalCount 
			from dbo.email_messages as m 
			where m.siteID = @siteID 
			and m.status = 'A' 
			and m.sendOnDate >= @today;
		</cfquery>
		
		<cfreturn local.qryMessage.totalCount>
	</cffunction>

	<cffunction name="showEmailActivity" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>

		<cfif not arguments.event.getValue('mc_adminToolInfo.myRights.viewEmailActivity')>
			<cflocation url="#this.link.message#&message=1">
		</cfif>
		
		<cfquery name="local.qryMessage" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			declare @messageID int, @recipients int, @siteID int;
			declare @pivotkeys TABLE (statuscode varchar(10), suffix varchar(10), pivotkey varchar(20));
			declare @messageStats TABLE (messageID int PRIMARY KEY, 
				[sg_open_total] int,[sg_click_total] int, [sg_drop_total] int,sg_bounce_total int, sg_block_total int, sg_spam_total int, suppressed_total int, optout_total int, 
				sg_open_unique int, sg_click_unique int, sg_drop_unique int, sg_bounce_unique int, sg_block_unique int, sg_spam_unique int, suppressed_unique int,  optout_unique int);

			set @messageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('msg',0)#">;
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			insert into @pivotkeys (statuscode,suffix,pivotkey)
			select statusesToTrack.listitem as statuscode,  suffixes.listitem as suffix, statusesToTrack.listitem + suffixes.listitem as pivotkey
			from membercentral.dbo.fn_varCharListToTable('sg_open,sg_click,sg_drop,sg_bounce,sg_block,sg_spam,suppressed,optout',',') statusesToTrack
			cross join membercentral.dbo.fn_varCharListToTable('_total,_unique',',') as suffixes;

			insert into @messageStats (messageID,
				[sg_open_total],[sg_click_total],[sg_drop_total],[sg_bounce_total],[sg_block_total], [sg_spam_total],[suppressed_total],[optout_total],
				[sg_open_unique],[sg_click_unique],[sg_drop_unique],[sg_bounce_unique],[sg_block_unique],[sg_spam_unique],[suppressed_unique],[optout_unique])
			select pivottable.messageID, 
				isnull(pivottable.[sg_open_total],0),isnull(pivottable.[sg_click_total],0),isnull(pivottable.[sg_drop_total],0),isnull(pivottable.[sg_bounce_total],0),isnull(pivottable.[sg_block_total],0),isnull(pivottable.[sg_spam_total],0),isnull(pivottable.[suppressed_total],0),isnull(pivottable.[optout_total],0),
				isnull(pivottable.[sg_open_unique],0),isnull(pivottable.[sg_click_unique],0),isnull(pivottable.[sg_drop_unique],0), isnull(pivottable.[sg_bounce_unique],0),isnull(pivottable.[sg_block_unique],0),isnull(pivottable.[sg_spam_unique],0),isnull(pivottable.[suppressed_unique],0),isnull(pivottable.[optout_unique],0)
			from (
				select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
				from dbo.email_messageRecipientHistory mrh 
				inner join dbo.email_messageRecipientHistoryTracking rt
					on mrh.siteID = @siteID
					and mrh.messageID = @messageID
					and rt.siteID = @siteID
					and mrh.recipientID = rt.recipientID
				inner join dbo.email_statuses rtst on rtst.statusID = rt.statusID
					and rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_block','sg_bounce','sg_spam')
				inner join @pivotkeys pk on pk.statusCode = rtst.statuscode
				group by mrh.messageID, pk.pivotkey, pk.suffix

				union 

				-- summary for statuses where recipient remains in final status, no entries in email_messageRecipientHistoryTracking
				select mrh.messageID, pk.pivotkey, case pk.suffix when '_total' then count(*) else count(distinct mrh.recipientID) end as pivotvalue
				from dbo.email_statuses rtst 
				inner join dbo.email_messageRecipientHistory mrh 
					on mrh.siteID = @siteID
					and mrh.messageID = @messageID
					and rtst.statusID = mrh.emailStatusID
					and rtst.statusCode in ('suppressed','optout')
				inner join @pivotkeys pk on pk.statusCode = rtst.statuscode
				group by mrh.messageID, pk.pivotkey, pk.suffix
			) as dataToPivot
			PIVOT (
			    sum(pivotvalue) for pivotkey in (
					[sg_open_total],[sg_click_total],[sg_drop_total],[sg_bounce_total],[sg_block_total],[sg_spam_total],[suppressed_total],[optout_total],
					[sg_open_unique],[sg_click_unique],[sg_drop_unique],[sg_bounce_unique],[sg_block_unique],[sg_spam_unique],[suppressed_unique],[optout_unique]) 
			) as pivottable;

			select @recipients = count(*) 
			from dbo.email_messageRecipientHistory
			where siteID = @siteID and messageID = @messageID;

			select m.messageid, m.dateEntered, m.sendOnDate, m.fromName, m.fromEmail,
				CASE WHEN mt.allowAdminView = 1 THEN m.subject ELSE ''+ mt.messageType +': For security, the content of this message is not viewable' END as subject,
				mt.messageType, mt.allowAdminView,
				CASE WHEN mt.allowAdminView = 1 THEN cv.rawContent ELSE ''+ mt.messageType +': For security, the content of this message is not viewable' END as rawContent,
				@recipients as recipients,
				(cast([sg_open_unique] as DECIMAL(18, 2)) / @recipients) as openrate,
				[sg_open_total] as totalOpens,
				[sg_open_unique] as uniqueOpens,
				[sg_click_total] as totalClicks,
				[sg_click_unique] as uniqueClicks,
				[optout_total] as totalOptouts,
				([sg_drop_unique] + [suppressed_total]) as totalSuppressed,
			 	([sg_bounce_unique] + [sg_block_unique] + [sg_spam_unique]) as recipientsWithProblems
			from dbo.email_messages as m
			inner join membercentral.dbo.cms_contentVersions as cv on cv.siteID = @siteID
				and cv.contentVersionID = m.contentVersionID
			inner join dbo.email_messageTypes as mt on mt.messageTypeID = m.messageTypeID
			left outer join @messageStats stats on stats.messageID = m.messageID
			where m.siteID = @siteID
			and m.messageID = @messageID
			and m.status = 'A';

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfquery name="local.qryClicks" datasource="#application.dsn.platformMail.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @clickStatusID int,  @siteID int = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;
			SELECT @clickStatusID = statusID FROM dbo.email_statuses WHERE statuscode = 'sg_click';

			select count(*) as clickCount
			from dbo.email_messageRecipientHistory mrh 
			inner join dbo.email_messageRecipientHistoryTracking rt
				on mrh.siteID = @siteID
				and mrh.messageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('msg',0)#">
				and rt.siteID = @siteID 
				and mrh.recipientID = rt.recipientID
			WHERE rt.statusID = @clickStatusID;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryMessage.recordcount is 0>
			<cflocation url="#this.link.searchEmailActivity#" addtoken="no">
		</cfif>

		<cfset local.openPercent = "0%">
		<cfset local.clickPercent = "0%">
		<cfset local.problemPercent = "0%">
		<cfset local.suppressedPercent = "0%">
		<cfset local.messageCountAfterOptOuts = local.qryMessage.recipients - val(local.qryMessage.totalOptouts)>
		<cfset local.messageCountAfterSuppressions = local.messageCountAfterOptOuts - val(local.qryMessage.totalSuppressed)>
		
		
		<cfif local.messageCountAfterSuppressions gt 0>
			<cfset local.openPercent = "#NumberFormat((val(local.qryMessage.uniqueOpens)/local.messageCountAfterSuppressions)*100, "__")#%">
		</cfif>
		<cfif local.messageCountAfterSuppressions gt 0>
			<cfset local.clickPercent = "#NumberFormat((val(local.qryMessage.uniqueClicks)/local.messageCountAfterSuppressions)*100, "__")#%">
		</cfif>
		<cfif local.messageCountAfterSuppressions gt 0>
			<cfset local.problemPercent = "#NumberFormat((val(local.qryMessage.recipientsWithProblems)/local.messageCountAfterSuppressions)*100, "__")#%">
		</cfif>
		<cfif local.messageCountAfterOptOuts gt 0>
			<cfset local.suppressedPercent = "#NumberFormat((val(local.qryMessage.totalSuppressed)/local.messageCountAfterOptOuts)*100, "__")#%">
		</cfif>




		<cfset local.recipientExportLink = buildCurrentLink(arguments.event,"exportRecipients") & "&msg=#local.qryMessage.messageID#&mode=stream" />
		<cfset local.qryStatuses = CreateObject('component', 'model.admin.emailBlast.emailBlast').getStatuses() />

		<cfset local.editMemberLink = buildLinkToTool(toolType='MemberAdmin',mca_ta='edit')>
		<cfset local.downloadMessageAttachmentLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=downloadMessageAttachment&mode=stream">
		<cfset local.resendEmailLink = "/?pg=admin&mca_ajaxlib=emailBlast&mca_ajaxfunc=resendEmail&mode=direct">
		<cfset local.recipientsJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=emailBlastJSON&meth=getActivityRecipientsList&msg=#local.qryMessage.messageid#&mode=stream">

		<cfset local.clickedLinksJSONLink = "#arguments.event.getValue('mc_adminNav.adminHomeResource')#&mca_jsonlib=mcdatatable&com=memberSettingsJSON&meth=getclickedLinks&messageID=#arguments.event.getValue('msg',0)#&mode=stream">
		
		<cfset appendBreadCrumbs(arguments.event,{ link='', text=encodeForHTML('Email Activity Detail: #local.qryMessage.subject#') })>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showActivity.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

	<cffunction name="exportRecipients" access="public" output="false" returntype="struct">
		<cfargument name="event" type="any">

		<cfset var local = StructNew()>
		<cfset local.data = "The export file could not be generated. Contact MemberCentral for assistance.">

		<!--- set vars --->
		<cfset local.strFolder = application.objDocDownload.createHoldingFolder(prefix=arguments.event.getValue('mc_siteInfo.sitecode'))>
		<cfset local.reportFileName = "EmailRecipients.csv">

		<cfquery name="local.qryActivity" datasource="#application.dsn.platformMail.dsn#">
			set nocount on;

			IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL
	      		DROP TABLE ##tmpExport;

			declare @recipients TABLE (recipientID int PRIMARY KEY);
			declare @recipientStats TABLE (recipientID int PRIMARY KEY, [sg_open] int,[sg_click] int,[sg_drop] int,[sg_bounce] int, [sg_block] int,[sg_spam] int);
			declare @messageID int, @siteID int, @dateEntered datetime, @sendOnDate datetime, @fromName varchar(300), 
				@fromEmail varchar(300), @subject varchar(300), @messageType varchar(15);

			set @messageID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('msg',0)#">;
			set @siteID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.siteid')#">;

			select @dateEntered=m.dateEntered, @sendOnDate=m.sendOnDate, @fromName=m.fromName, @fromEmail=m.fromEmail, 
				@subject=m.subject, @messageType=mt.messageType
			from dbo.email_messages as m
			inner join dbo.email_messageTypes as mt on mt.messageTypeID = m.messageTypeID
			where m.siteID = @siteID
			and m.messageID = @messageID;

			insert into @recipients (recipientID)
			select mrh.recipientID
			from dbo.email_messageRecipientHistory as mrh
			inner join dbo.email_messages as m 
				on m.siteID = @siteID
				and m.status = 'A'
				and m.messageID = mrh.messageID
				and m.messageID = @messageID
			<cfif len(arguments.event.getTrimValue('statusCode',''))>
				inner join email_statuses est on est.statuscode in (<cfqueryparam cfsqltype="cf_sql_varchar" list="true" value="#urldecode(arguments.event.getValue('statusCode'))#">)
					and est.statusID = mrh.emailStatusID
			</cfif>
			where mrh.siteID = @siteID
			<cfif len(arguments.event.getTrimValue('recipientName',''))>
				and mrh.toName like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getValue('recipientName')#%">
			</cfif>
			<cfif len(arguments.event.getTrimValue('recipientEmail',''))>
				and mrh.toEmail like <cfqueryparam cfsqltype="cf_sql_varchar" value="%#arguments.event.getValue('recipientEmail')#%">
			</cfif>;

			insert into @recipientStats (recipientID,sg_open,sg_click,sg_drop,sg_bounce,sg_block,sg_spam)
			select pivottable.recipientID, isnull(pivottable.sg_open,0),isnull(pivottable.sg_click,0),isnull(pivottable.sg_drop,0),isnull(pivottable.sg_bounce,0),isnull(pivottable.sg_block,0),isnull(pivottable.sg_spam,0)
			from (
				select r.recipientID, rtst.statusCode, count(*) as pivotvalue
				from @recipients r
				inner join dbo.email_messageRecipientHistoryTracking rt 
					on rt.siteID = @siteID
					and rt.recipientID = r.recipientID
				inner join dbo.email_statuses rtst on rtst.statusID = rt.statusID
					and rtst.statusCode in ('sg_open','sg_click','sg_drop','sg_bounce','sg_block','sg_spam')
				group by r.recipientID,rtst.statusCode
			) as dataToPivot
			PIVOT (
			    sum(pivotvalue) for statusCode in ([sg_open],[sg_click],[sg_drop],[sg_bounce],[sg_block],[sg_spam]) 
			) as pivottable;
 
			select data.recipientID, data.ToName, data.toEmail, data.dateLastUpdated, data.status,
				[sg_open] as totalOpens,
				[sg_click] as totalClicks,
				suppressed = case when data.statusCode IN ('suppressed') or sg_drop > 0 then 'True' else 'False' end,
				hasProblems = case when (sg_bounce + sg_block + sg_spam) > 0 then 'True' else 'False' end,
				company, 
				membernumber, 
				memberName,
				@dateEntered as messageCreatedDate, 
				@sendOnDate as messageSentDate, 
				@fromName as fromName, 
				@fromEmail as fromEmail, 
				@subject as subject, 
				@messageType as messageType
			into ##tmpExport
			from (
				select mrh.recipientID, mrh.ToName, mrh.toEmail, mrh.dateLastUpdated, s.status, s.statusCode,
					m.company, m.membernumber, m.memberID, 
					mActive.lastname + ', ' + mActive.firstname + isnull(' ' + mActive.middlename,'') + ' (' + mActive.membernumber + ')' as memberName
				from @recipients r
				inner join dbo.email_messageRecipientHistory as mrh on mrh.recipientID = r.recipientID
				inner join dbo.email_statuses as s on s.statusID = mrh.emailStatusID
				inner join membercentral.dbo.ams_members m on m.memberID = mrh.memberID
				inner join membercentral.dbo.ams_members mactive on mactive.memberID = m.activememberID
			) as data
			left outer join @recipientStats stats on stats.recipientID = data.recipientID
			order by data.recipientID;

			DECLARE @selectsql varchar(max) = '
				SELECT *, ROW_NUMBER() OVER(order by recipientID) as mcCSVorder 
				*FROM* ##tmpExport';
			EXEC membercentral.dbo.up_queryToCSV @selectsql=@selectsql, @csvfilename='#application.objCommon.convertFileSeparator("#local.strFolder.folderPathUNC#/#local.reportFileName#",'\')#', @returnColumns=0;

			IF OBJECT_ID('tempdb..##tmpExport') IS NOT NULL
	      		DROP TABLE ##tmpExport;
		</cfquery>

		<cfset application.objDocDownload.waitForFileExists(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#")>
		<cfset local.stDownloadURL = application.objDocDownload.createDownloadURL(sourceFilePath="#local.strFolder.folderPath#/#local.reportFileName#", displayName=local.reportFileName, deleteSourceFile=1)>

		<cfsavecontent variable="local.data">
			<cfoutput>
			<script type="text/javascript">doExportActivity('#local.stDownloadURL#');</script>
			</cfoutput>
		</cfsavecontent>
		
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="message" access="public" output="false" returntype="struct" hint="Messages for this App">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
		</cfscript>
		<cfsavecontent variable="local.data">
			<cfoutput>
				<h4>We're Sorry...</h4>
				<cfif arguments.event.valueExists('msg')>
					<p>
						<cfswitch expression="#arguments.event.getValue('msg')#">
							<cfcase value="1"><b>You do not have rights to this page.</b></cfcase>
							<cfcase value="2"><b>You did not select which Member Field Set to use when searching for members.</b></cfcase>
							<cfcase value="3"><b>You did not select which Member Field Set to use when showing search results.</b></cfcase>
						</cfswitch>
					</p>
				</cfif>
			</cfoutput>
		</cfsavecontent>
		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>
	
	<cffunction name="doClassificationMove" access="public" output="false" returntype="struct" hint="Re-Order Fields">
		<cfargument name="classificationID" type="numeric" required="true" />
		<cfargument name="dir" type="string" required="true">

		<cfset var local = structNew() />

		<cftry>
			<cfstoredproc procedure="ams_moveClassifications" datasource="#application.dsn.membercentral.dsn#">
				<cfprocparam type="In" value="#arguments.classificationID#" cfsqltype="cf_sql_integer" />
				<cfprocparam type="In" cfsqltype="cf_sql_varchar" value="#arguments.dir#">
			</cfstoredproc>

			<cfset local.data.success = true />
		<cfcatch type="Any">
			<cfset application.objError.sendError(cfcatch=cfcatch)>
			<cfset local.data.success = false>
		</cfcatch>
		</cftry>

		<cfreturn local.data />
	</cffunction>

	<cffunction name="showEstimatedMonthlyBilling" access="public" output="false" returntype="struct">
		<cfargument name="Event" type="any">
		
		<cfset var local = structNew()>
		<cfset local.firstOfThisMonth = createDate(year(now()), month(now()), 1)>
		<cfset local.firstOfNextMonth = DateAdd('m',1,local.firstOfThisMonth)>

		<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="email_generateMonthlyBillingBySite">
			<cfprocparam type="in" cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteInfo.siteid')#">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.firstOfThisMonth#">
			<cfprocparam type="in" cfsqltype="CF_SQL_TIMESTAMP" value="#dateformat(now(), "m/d/yyyy")# 23:59:59.997">
			<cfprocparam type="in" cfsqltype="CF_SQL_DATE" value="#local.firstOfNextMonth#">
			<cfprocresult name="local.qryActivity" resultset="1">
		</cfstoredproc>

		<cfsavecontent variable="local.data">
			<cfinclude template="dsp_showEstimatedMonthlyBilling.cfm">
		</cfsavecontent>

		<cfreturn returnAppStruct(local.data,"echo")>
	</cffunction>

</cfcomponent>